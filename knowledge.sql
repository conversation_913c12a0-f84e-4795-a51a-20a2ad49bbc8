/*
 Navicat Premium Data Transfer

 Source Server         : mysql
 Source Server Type    : MySQL
 Source Server Version : 80034 (8.0.34)
 Source Host           : localhost:3306
 Source Schema         : knowledge

 Target Server Type    : MySQL
 Target Server Version : 80034 (8.0.34)
 File Encoding         : 65001

 Date: 15/05/2025 14:23:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for article_tags
-- ----------------------------
DROP TABLE IF EXISTS `article_tags`;
CREATE TABLE `article_tags`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `article_id` int NOT NULL,
  `tag_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of article_tags
-- ----------------------------
INSERT INTO `article_tags` VALUES (1, 1, 2);
INSERT INTO `article_tags` VALUES (2, 1, 3);
INSERT INTO `article_tags` VALUES (3, 1, 4);
INSERT INTO `article_tags` VALUES (4, 2, 7);
INSERT INTO `article_tags` VALUES (5, 2, 8);
INSERT INTO `article_tags` VALUES (6, 3, 5);
INSERT INTO `article_tags` VALUES (7, 4, 12);
INSERT INTO `article_tags` VALUES (8, 4, 13);
INSERT INTO `article_tags` VALUES (9, 5, 10);
INSERT INTO `article_tags` VALUES (10, 5, 11);

-- ----------------------------
-- Table structure for articles
-- ----------------------------
DROP TABLE IF EXISTS `articles`;
CREATE TABLE `articles`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `category_id` int NOT NULL,
  `author_id` int NOT NULL,
  `status` enum('draft','published','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'draft',
  `view_count` int NULL DEFAULT 0,
  `like_count` int NULL DEFAULT 0,
  `comment_count` int NULL DEFAULT 0,
  `is_featured` tinyint(1) NULL DEFAULT 0,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of articles
-- ----------------------------
INSERT INTO `articles` VALUES (1, 'Spring Boot 入门指南', '# Spring Boot 入门指南\n\n本文将介绍如何使用Spring Boot快速构建一个Web应用。\n\n## 什么是Spring Boot\n\nSpring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化Spring应用的初始搭建以及开发过程。\n\n## 环境准备\n\n- JDK 1.8+\n- Maven 3.0+\n- IDE（推荐IntelliJ IDEA）\n\n## 创建项目\n\n可以通过Spring Initializr网站创建项目：https://start.spring.io/\n\n## 添加依赖\n\n```xml\n<dependency>\n    <groupId>org.springframework.boot</groupId>\n    <artifactId>spring-boot-starter-web</artifactId>\n</dependency>\n```\n\n## 编写代码\n\n```java\n@SpringBootApplication\npublic class Application {\n    public static void main(String[] args) {\n        SpringApplication.run(Application.class, args);\n    }\n}\n```\n\n## 运行应用\n\n执行`mvn spring-boot:run`命令或直接运行main方法。\n\n## 总结\n\nSpring Boot大大简化了Spring应用的开发流程，让开发者可以更专注于业务逻辑的实现。', 'Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化Spring应用的初始搭建以及开发过程。本文将介绍如何使用Spring Boot快速构建一个Web应用。', 'https://via.placeholder.com/800x400?text=Spring+Boot', 3, 1, 'published', 1250, 78, 15, 1, 'aaa', '2025-05-11 11:23:54', 'admin', '2025-05-11 11:23:54', 0);
INSERT INTO `articles` VALUES (2, 'Vue3 组合式API详解', '# Vue3 组合式API详解\n\n本文将详细介绍Vue3中的组合式API（Composition API）及其使用方法。\n\n## 什么是组合式API\n\n组合式API是Vue3中新增的一种编写组件逻辑的方式，它允许我们使用导入的API函数来描述组件逻辑。\n\n## setup函数\n\n```javascript\nexport default {\n  setup() {\n    const count = ref(0)\n    const increment = () => {\n      count.value++\n    }\n    \n    return {\n      count,\n      increment\n    }\n  }\n}\n```\n\n## 响应式API\n\n- ref：用于基本类型的响应式引用\n- reactive：用于对象类型的响应式代理\n- computed：创建一个计算属性\n- watch：监听响应式数据的变化\n\n## 生命周期钩子\n\n- onMounted\n- onUpdated\n- onUnmounted\n- 等等\n\n## 与选项式API的对比\n\n组合式API相比选项式API有以下优势：\n\n1. 更好的逻辑复用\n2. 更灵活的代码组织\n3. 更好的类型推导\n4. 更小的打包体积\n\n## 总结\n\n组合式API为Vue组件的编写提供了更大的灵活性，特别适合构建大型应用。', 'Vue3中的组合式API（Composition API）是一种全新的编写组件逻辑的方式，本文将详细介绍其使用方法及优势。', 'https://via.placeholder.com/800x400?text=Vue3', 2, 2, 'published', 980, 65, 12, 1, 'bbb', '2025-05-11 11:23:54', 'user1', '2025-05-11 11:23:54', 0);
INSERT INTO `articles` VALUES (3, 'MySQL性能优化实战', '# MySQL性能优化实战\n\n本文将分享一些MySQL数据库性能优化的实用技巧。\n\n## 索引优化\n\n索引是提高查询性能的关键。以下是一些索引优化的建议：\n\n- 为常用的查询字段创建索引\n- 避免在索引列上使用函数\n- 使用复合索引时注意列的顺序\n- 定期分析和优化索引\n\n## SQL语句优化\n\n```sql\n-- 优化前\nSELECT * FROM users WHERE status = 1;\n\n-- 优化后\nSELECT id, name, email FROM users WHERE status = 1;\n```\n\n## 服务器配置优化\n\n以下是一些重要的MySQL配置参数：\n\n- innodb_buffer_pool_size\n- innodb_log_file_size\n- max_connections\n- query_cache_size\n\n## 表结构优化\n\n- 选择合适的数据类型\n- 使用规范化设计\n- 适当的反规范化\n- 使用分区表\n\n## 监控与分析\n\n- 使用EXPLAIN分析查询\n- 开启慢查询日志\n- 使用性能监控工具\n\n## 总结\n\nMySQL性能优化是一个持续的过程，需要从多个方面入手，不断调整和改进。', 'MySQL性能优化是数据库管理的重要环节，本文将分享一些实用的优化技巧，包括索引优化、SQL语句优化、服务器配置优化等方面。', 'https://via.placeholder.com/800x400?text=MySQL', 4, 1, 'published', 1560, 92, 20, 1, 'aaa', '2025-05-11 11:23:54', 'admin', '2025-05-11 11:23:54', 0);
INSERT INTO `articles` VALUES (4, 'Docker容器化应用实践', '# Docker容器化应用实践\n\n本文将介绍如何使用Docker容器化你的应用。\n\n## Docker简介\n\nDocker是一个开源的应用容器引擎，让开发者可以打包他们的应用以及依赖包到一个可移植的容器中。\n\n## 安装Docker\n\n根据你的操作系统，按照官方文档安装Docker：https://docs.docker.com/get-docker/\n\n## 创建Dockerfile\n\n```dockerfile\nFROM openjdk:11-jre-slim\nWORKDIR /app\nCOPY target/myapp.jar /app/\nEXPOSE 8080\nCMD [\"java\", \"-jar\", \"myapp.jar\"]\n```\n\n## 构建镜像\n\n```bash\ndocker build -t myapp:1.0 .\n```\n\n## 运行容器\n\n```bash\ndocker run -p 8080:8080 myapp:1.0\n```\n\n## Docker Compose\n\n对于多容器应用，可以使用Docker Compose：\n\n```yaml\nversion: \"3\"\nservices:\n  app:\n    build: .\n    ports:\n      - \"8080:8080\"\n  db:\n    image: mysql:5.7\n    environment:\n      MYSQL_ROOT_PASSWORD: root\n      MYSQL_DATABASE: mydb\n```\n\n## 最佳实践\n\n- 使用多阶段构建\n- 最小化镜像大小\n- 使用非root用户运行容器\n- 使用健康检查\n\n## 总结\n\nDocker容器化技术可以大大简化应用的部署和运维工作，提高开发效率和系统可靠性。', 'Docker是一个开源的应用容器引擎，本文将介绍如何使用Docker容器化你的应用，包括创建Dockerfile、构建镜像、运行容器等实用技巧。', 'https://via.placeholder.com/800x400?text=Docker', 5, 3, 'published', 870, 58, 10, 1, 'ccc', '2025-05-11 11:23:54', 'user2', '2025-05-11 11:23:54', 0);
INSERT INTO `articles` VALUES (5, '深度学习入门：TensorFlow实战', '# 深度学习入门：TensorFlow实战\n\n本文将介绍如何使用TensorFlow框架入门深度学习。\n\n## TensorFlow简介\n\nTensorFlow是由Google开发的开源机器学习框架，广泛应用于深度学习模型的训练和部署。\n\n## 环境搭建\n\n```bash\npip install tensorflow\n```\n\n## 基本概念\n\n- 张量（Tensor）\n- 计算图（Graph）\n- 会话（Session）\n- 变量（Variable）\n\n## 构建简单神经网络\n\n```python\nimport tensorflow as tf\n\n# 创建模型\nmodel = tf.keras.Sequential([\n  tf.keras.layers.Dense(128, activation=\"relu\"),\n  tf.keras.layers.Dense(10, activation=\"softmax\")\n])\n\n# 编译模型\nmodel.compile(\n  optimizer=\"adam\",\n  loss=\"sparse_categorical_crossentropy\",\n  metrics=[\"accuracy\"]\n)\n\n# 训练模型\nmodel.fit(x_train, y_train, epochs=5)\n```\n\n## 模型评估与预测\n\n```python\n# 评估模型\nmodel.evaluate(x_test, y_test)\n\n# 预测\npredictions = model.predict(x_test)\n```\n\n## 保存与加载模型\n\n```python\n# 保存模型\nmodel.save(\"my_model.h5\")\n\n# 加载模型\nloaded_model = tf.keras.models.load_model(\"my_model.h5\")\n```\n\n## 总结\n\nTensorFlow提供了丰富的API和工具，使深度学习模型的开发变得更加简单和高效。', 'TensorFlow是由Google开发的开源机器学习框架，本文将介绍如何使用TensorFlow入门深度学习，包括环境搭建、基本概念、构建神经网络等内容。', 'https://via.placeholder.com/800x400?text=TensorFlow', 8, 4, 'published', 1120, 75, 18, 1, 'ddd', '2025-05-11 11:23:54', 'editor', '2025-05-11 11:23:54', 0);

-- ----------------------------
-- Table structure for carousel_items
-- ----------------------------
DROP TABLE IF EXISTS `carousel_items`;
CREATE TABLE `carousel_items`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sort_order` int NULL DEFAULT 0,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'active',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of carousel_items
-- ----------------------------

-- ----------------------------
-- Table structure for categories
-- ----------------------------
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `parent_id` int NULL DEFAULT 0,
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sort_order` int NULL DEFAULT 0,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of categories
-- ----------------------------
INSERT INTO `categories` VALUES (1, '技术', '技术相关文章', NULL, 'fa-code', 1, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (2, '前端', '前端开发技术', 1, 'fa-html5', 1, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (3, '后端', '后端开发技术', 1, 'fa-server', 2, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (4, '数据库', '数据库技术', 1, 'fa-database', 3, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (5, 'DevOps', '运维技术', 1, 'fa-cogs', 4, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (6, '人工智能', 'AI相关技术', NULL, 'fa-robot', 2, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (7, '机器学习', '机器学习技术', 6, 'fa-brain', 1, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (8, '深度学习', '深度学习技术', 6, 'fa-network-wired', 2, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (9, '职场', '职场相关内容', NULL, 'fa-briefcase', 3, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `categories` VALUES (10, '求职', '求职相关内容', 9, 'fa-search', 1, 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `article_id` int NOT NULL,
  `user_id` int NOT NULL,
  `parent_id` int NULL DEFAULT 0,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of comments
-- ----------------------------
INSERT INTO `comments` VALUES (1, '非常详细的教程，对我帮助很大！', 1, 2, NULL, 'user1', '2025-05-11 11:23:54', 'user1', '2025-05-11 11:23:54', 0);
INSERT INTO `comments` VALUES (2, '请问如何处理Spring Boot中的跨域问题？', 1, 3, NULL, 'user2', '2025-05-11 11:23:54', 'user2', '2025-05-11 11:23:54', 0);
INSERT INTO `comments` VALUES (3, '可以使用@CrossOrigin注解或配置CorsFilter', 1, 1, 2, 'admin', '2025-05-11 11:23:54', 'admin', '2025-05-11 11:23:54', 0);
INSERT INTO `comments` VALUES (4, 'Vue3的组合式API确实比选项式API更灵活', 2, 1, NULL, 'admin', '2025-05-11 11:23:54', 'admin', '2025-05-11 11:23:54', 0);
INSERT INTO `comments` VALUES (5, '这篇文章对MySQL索引优化讲解得很透彻', 3, 4, NULL, 'editor', '2025-05-11 11:23:54', 'editor', '2025-05-11 11:23:54', 0);
INSERT INTO `comments` VALUES (6, 'Docker确实简化了应用部署流程', 4, 2, NULL, 'user1', '2025-05-11 11:23:54', 'user1', '2025-05-11 11:23:54', 0);
INSERT INTO `comments` VALUES (7, 'TensorFlow入门不容易，这篇文章讲得很清晰', 5, 3, NULL, 'user2', '2025-05-11 11:23:54', 'user2', '2025-05-11 11:23:54', 0);

-- ----------------------------
-- Table structure for competition_participants
-- ----------------------------
DROP TABLE IF EXISTS `competition_participants`;
CREATE TABLE `competition_participants`  (
  `competition_id` int NOT NULL,
  `user_id` int NOT NULL,
  `team_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `registration_date` datetime NULL DEFAULT NULL,
  `status` enum('registered','approved','rejected','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'registered',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of competition_participants
-- ----------------------------

-- ----------------------------
-- Table structure for competitions
-- ----------------------------
DROP TABLE IF EXISTS `competitions`;
CREATE TABLE `competitions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `prize` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `status` enum('upcoming','ongoing','completed','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'upcoming',
  `participant_count` int NULL DEFAULT 0,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of competitions
-- ----------------------------

-- ----------------------------
-- Table structure for experience_tags
-- ----------------------------
DROP TABLE IF EXISTS `experience_tags`;
CREATE TABLE `experience_tags`  (
  `experience_id` int NOT NULL,
  `tag_id` int NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of experience_tags
-- ----------------------------

-- ----------------------------
-- Table structure for experiences
-- ----------------------------
DROP TABLE IF EXISTS `experiences`;
CREATE TABLE `experiences`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `author_id` int NOT NULL,
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `graduation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `view_count` int NULL DEFAULT 0,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of experiences
-- ----------------------------

-- ----------------------------
-- Table structure for project_tags
-- ----------------------------
DROP TABLE IF EXISTS `project_tags`;
CREATE TABLE `project_tags`  (
  `project_id` int NOT NULL,
  `tag_id` int NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of project_tags
-- ----------------------------

-- ----------------------------
-- Table structure for projects
-- ----------------------------
DROP TABLE IF EXISTS `projects`;
CREATE TABLE `projects`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `author_id` int NOT NULL,
  `status` enum('ongoing','completed','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'ongoing',
  `view_count` int NULL DEFAULT 0,
  `like_count` int NULL DEFAULT 0,
  `comment_count` int NULL DEFAULT 0,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of projects
-- ----------------------------

-- ----------------------------
-- Table structure for tags
-- ----------------------------
DROP TABLE IF EXISTS `tags`;
CREATE TABLE `tags`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tags
-- ----------------------------
INSERT INTO `tags` VALUES (1, 'Java', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (2, 'Spring', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (3, 'Spring Boot', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (4, 'MyBatis', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (5, 'MySQL', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (6, 'Redis', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (7, 'JavaScript', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (8, 'Vue', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (9, 'React', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (10, 'Python', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (11, 'TensorFlow', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (12, 'Docker', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (13, 'Kubernetes', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (14, '微服务', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (15, '面试', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);
INSERT INTO `tags` VALUES (16, '算法', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0);

-- ----------------------------
-- Table structure for tool_tags
-- ----------------------------
DROP TABLE IF EXISTS `tool_tags`;
CREATE TABLE `tool_tags`  (
  `tool_id` int NOT NULL,
  `tag_id` int NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tool_tags
-- ----------------------------

-- ----------------------------
-- Table structure for tools
-- ----------------------------
DROP TABLE IF EXISTS `tools`;
CREATE TABLE `tools`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `category_id` int NOT NULL,
  `submitted_by` int NOT NULL,
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tools
-- ----------------------------

-- ----------------------------
-- Table structure for user_favorites
-- ----------------------------
DROP TABLE IF EXISTS `user_favorites`;
CREATE TABLE `user_favorites`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `content_type` enum('article','project','tool','experience') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_id` int NOT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_favorites
-- ----------------------------
INSERT INTO `user_favorites` VALUES (1, 1, 'article', 1, NULL, NULL, NULL, NULL, 0);
INSERT INTO `user_favorites` VALUES (4, 6, 'article', 1, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for user_likes
-- ----------------------------
DROP TABLE IF EXISTS `user_likes`;
CREATE TABLE `user_likes`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `content_type` enum('article','project','comment','experience') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_id` int NOT NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_likes
-- ----------------------------
INSERT INTO `user_likes` VALUES (1, 1, 'article', 1, NULL, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `role` enum('admin','editor','user') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'user',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '0-正常,1-删除',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', '$2a$10$X/XQFx1AYeEZEpS2HrGBQuVxqUMNBNi7aW8a4JcYdLx.1KL3S5iEe', '<EMAIL>', 'https://via.placeholder.com/150', 'admin', '系统管理员', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0, 'aaa');
INSERT INTO `users` VALUES (2, 'user1', '$2a$10$X/XQFx1AYeEZEpS2HrGBQuVxqUMNBNi7aW8a4JcYdLx.1KL3S5iEe', '<EMAIL>', 'https://via.placeholder.com/150', 'user', '普通用户1', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0, 'bbb');
INSERT INTO `users` VALUES (3, 'user2', '$2a$10$X/XQFx1AYeEZEpS2HrGBQuVxqUMNBNi7aW8a4JcYdLx.1KL3S5iEe', '<EMAIL>', 'https://via.placeholder.com/150', 'user', '普通用户2', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0, 'ccc');
INSERT INTO `users` VALUES (4, 'editor', '$2a$10$X/XQFx1AYeEZEpS2HrGBQuVxqUMNBNi7aW8a4JcYdLx.1KL3S5iEe', '<EMAIL>', 'https://via.placeholder.com/150', 'editor', '内容编辑', 'system', '2025-05-11 11:23:54', 'system', '2025-05-11 11:23:54', 0, 'ddd');
INSERT INTO `users` VALUES (6, '15848794218', 'e1eea8c1901309e516355d163a89b6ec', '<EMAIL>', NULL, 'user', NULL, NULL, NULL, NULL, NULL, 0, 'hou');
INSERT INTO `users` VALUES (7, '654456', '0b8a061a958815774ea9b1051aa7faf6', '<EMAIL>', NULL, 'user', NULL, NULL, NULL, NULL, NULL, 0, '大顶堆');
INSERT INTO `users` VALUES (8, 'zjm_0513', 'd735eac7933f2f386ec77c8468cf8206', '<EMAIL>', NULL, 'user', NULL, NULL, NULL, NULL, NULL, 0, '水水');

SET FOREIGN_KEY_CHECKS = 1;
