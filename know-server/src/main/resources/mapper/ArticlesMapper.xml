<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.ArticlesMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.Articles">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="summary" column="summary" jdbcType="VARCHAR"/>
            <result property="coverImage" column="cover_image" jdbcType="VARCHAR"/>
            <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
            <result property="authorId" column="author_id" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="OTHER"/>
            <result property="viewCount" column="view_count" jdbcType="INTEGER"/>
            <result property="likeCount" column="like_count" jdbcType="INTEGER"/>
            <result property="commentCount" column="comment_count" jdbcType="INTEGER"/>
            <result property="isFeatured" column="is_featured" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,content,
        summary,cover_image,category_id,
        author_id,status,view_count,
        like_count,comment_count,is_featured,
        create_by,create_time,update_by,
        update_time,del_flag
    </sql>
</mapper>
