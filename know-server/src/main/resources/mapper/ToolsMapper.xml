<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.ToolsMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.Tools">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="icon" column="icon" jdbcType="VARCHAR"/>
            <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
            <result property="submittedBy" column="submitted_by" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="OTHER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,description,
        url,icon,category_id,
        submitted_by,status,create_by,
        create_time,update_by,update_time,
        del_flag
    </sql>
</mapper>
