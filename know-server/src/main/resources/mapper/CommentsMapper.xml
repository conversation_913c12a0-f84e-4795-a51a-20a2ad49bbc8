<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.CommentsMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.Comments">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="articleId" column="article_id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,content,article_id,
        user_id,parent_id,create_by,
        create_time,update_by,update_time,
        del_flag
    </sql>
</mapper>
