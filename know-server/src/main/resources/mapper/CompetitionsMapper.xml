<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.CompetitionsMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.Competitions">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="rules" column="rules" jdbcType="VARCHAR"/>
            <result property="prize" column="prize" jdbcType="VARCHAR"/>
            <result property="coverImage" column="cover_image" jdbcType="VARCHAR"/>
            <result property="startDate" column="start_date" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="OTHER"/>
            <result property="participantCount" column="participant_count" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,description,
        rules,prize,cover_image,
        start_date,end_date,status,
        participant_count,create_by,create_time,
        update_by,update_time,del_flag
    </sql>
</mapper>
