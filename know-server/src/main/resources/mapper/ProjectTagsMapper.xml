<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.ProjectTagsMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.ProjectTags">
            <result property="projectId" column="project_id" jdbcType="INTEGER"/>
            <result property="tagId" column="tag_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        project_id,tag_id
    </sql>
</mapper>
