<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.UserLikesMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.UserLikes">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="contentType" column="content_type" jdbcType="OTHER"/>
            <result property="contentId" column="content_id" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,content_type,
        content_id,create_by,create_time,
        update_by,update_time,del_flag
    </sql>
</mapper>
