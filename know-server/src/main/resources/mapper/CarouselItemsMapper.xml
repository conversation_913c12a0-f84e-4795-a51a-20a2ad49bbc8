<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.CarouselItemsMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.CarouselItems">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="image" column="image" jdbcType="VARCHAR"/>
            <result property="link" column="link" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="OTHER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,description,
        image,link,sort_order,
        status,create_by,create_time,
        update_by,update_time,del_flag
    </sql>
</mapper>
