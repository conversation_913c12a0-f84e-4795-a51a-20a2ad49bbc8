<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.houyaozu.knowledge.server.mapper.ExperiencesMapper">

    <resultMap id="BaseResultMap" type="com.houyaozu.knowledge.pojo.domain.Experiences">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="summary" column="summary" jdbcType="VARCHAR"/>
            <result property="authorId" column="author_id" jdbcType="INTEGER"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="graduation" column="graduation" jdbcType="VARCHAR"/>
            <result property="viewCount" column="view_count" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,content,
        summary,author_id,position,
        graduation,view_count,create_by,
        create_time,update_by,update_time,
        del_flag
    </sql>
</mapper>
