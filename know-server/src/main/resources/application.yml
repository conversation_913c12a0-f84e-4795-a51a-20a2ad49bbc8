server:
  port: 8080
  servlet:
    context-path: /api
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **********************************************************************************************************************************************
    username: root
    password: 134513
    hikari:
      connection-test-query: SELECT 1 # 自动检测连接
      connection-timeout: 60000 #数据库连接超时时间,默认30秒
      idle-timeout: 500000 #空闲连接存活最大时间，默认600000（10分钟）
      max-lifetime: 540000 #此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      maximum-pool-size: 12 #连接池最大连接数，默认是10
      minimum-idle: 10 #最小空闲连接数量
      pool-name: SPHHikariPool # 连接池名称
  jackson:
    time-zone: GMT+8
  ai:
    ollama:
      base-url: http://*************:11434
      chat:
        model: deepseek-r1
  data:
    redis:
      host: ***************
      port: 6379
      database: 1
logging:
  level:
    org.springframework.ai.chat.client.advisor: debug
    com.houyaozu.knowledge: debug


#用于打印框架生成的sql语句，便于调试
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
springdoc:
  default-flat-param-object: true
